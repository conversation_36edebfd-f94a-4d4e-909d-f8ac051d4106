import { _decorator, Component, input, Input, KeyCode } from 'cc';
import { BackgroundManager } from './BackgroundManager';
const { ccclass, property } = _decorator;

/**
 * 背景自动配置器 - 自动配置MoveBg组件的target引用
 */
@ccclass('BackgroundAutoConfig')
export class BackgroundAutoConfig extends Component {

    start() {
        // 注册键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onDestroy() {
        // 取消键盘事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: any) {
        switch (event.keyCode) {
            case KeyCode.KEY_A:
                // A键自动配置所有背景
                this.autoConfigureAllBackgrounds();
                break;
        }
    }

    /**
     * 自动配置所有背景的MoveBg组件
     */
    public autoConfigureAllBackgrounds(): void {
        const bgManager = BackgroundManager.getInstance();
        if (!bgManager) {
            console.error("BackgroundManager未找到！");
            return;
        }

        console.log("=== 开始自动配置背景 ===");

        // 配置每套背景
        for (let i = 1; i <= 3; i++) {
            this.configureBackgroundSet(bgManager, i);
        }

        console.log("=== 背景自动配置完成 ===");
    }

    /**
     * 配置指定背景套装
     */
    private configureBackgroundSet(bgManager: BackgroundManager, setNumber: number): void {
        console.log(`\n--- 配置背景套装 ${setNumber} ---`);

        let bgNode, landNode;
        switch (setNumber) {
            case 1:
                bgNode = bgManager['bgSet1'];
                landNode = bgManager['landSet1'];
                break;
            case 2:
                bgNode = bgManager['bgSet2'];
                landNode = bgManager['landSet2'];
                break;
            case 3:
                bgNode = bgManager['bgSet3'];
                landNode = bgManager['landSet3'];
                break;
        }

        // 配置背景MoveBg
        if (bgNode) {
            this.configureMoveBg(bgNode, `Bg_Set${setNumber}`);
        } else {
            console.warn(`背景节点 ${setNumber} 未找到`);
        }

        // 配置地面MoveBg
        if (landNode) {
            this.configureMoveBg(landNode, `Land_Set${setNumber}`);
        } else {
            console.warn(`地面节点 ${setNumber} 未找到`);
        }
    }

    /**
     * 配置MoveBg组件
     */
    private configureMoveBg(parentNode: any, nodeName: string): void {
        const moveBg = parentNode.getComponent('MoveBg');
        if (!moveBg) {
            console.warn(`${nodeName} 没有MoveBg组件`);
            return;
        }

        const children = parentNode.children;
        if (children.length < 2) {
            console.warn(`${nodeName} 子节点数量不足 (需要至少2个): ${children.length}`);
            return;
        }

        // 自动分配前两个子节点作为target1ToMove和target2ToMove
        const target1 = children[0];
        const target2 = children[1];

        moveBg.target1ToMove = target1;
        moveBg.target2ToMove = target2;

        console.log(`${nodeName} MoveBg配置完成:`);
        console.log(`  target1ToMove: ${target1.name}`);
        console.log(`  target2ToMove: ${target2.name}`);

        // 重置位置确保正确的循环移动
        this.resetTargetPositions(target1, target2);
    }

    /**
     * 重置目标节点位置
     */
    private resetTargetPositions(target1: any, target2: any): void {
        // 设置初始位置，确保能正确循环
        target1.setPosition(0, target1.position.y);
        target2.setPosition(728, target2.position.y); // 728是背景宽度

        console.log(`  重置位置: target1(0, ${target1.position.y}), target2(728, ${target2.position.y})`);
    }

    /**
     * 验证配置
     */
    public validateConfiguration(): void {
        const bgManager = BackgroundManager.getInstance();
        if (!bgManager) {
            console.error("BackgroundManager未找到！");
            return;
        }

        console.log("=== 验证背景配置 ===");

        for (let i = 1; i <= 3; i++) {
            console.log(`\n--- 验证背景套装 ${i} ---`);

            let bgNode, landNode;
            switch (i) {
                case 1:
                    bgNode = bgManager['bgSet1'];
                    landNode = bgManager['landSet1'];
                    break;
                case 2:
                    bgNode = bgManager['bgSet2'];
                    landNode = bgManager['landSet2'];
                    break;
                case 3:
                    bgNode = bgManager['bgSet3'];
                    landNode = bgManager['landSet3'];
                    break;
            }

            this.validateMoveBg(bgNode, `Bg_Set${i}`);
            this.validateMoveBg(landNode, `Land_Set${i}`);
        }
    }

    /**
     * 验证MoveBg配置
     */
    private validateMoveBg(node: any, nodeName: string): void {
        if (!node) {
            console.warn(`${nodeName} 节点不存在`);
            return;
        }

        const moveBg = node.getComponent('MoveBg');
        if (!moveBg) {
            console.warn(`${nodeName} 没有MoveBg组件`);
            return;
        }

        const target1 = moveBg.target1ToMove;
        const target2 = moveBg.target2ToMove;

        const isValid = target1 && target2;
        console.log(`${nodeName}: ${isValid ? '✓' : '✗'}`);
        
        if (isValid) {
            console.log(`  target1: ${target1.name} (${target1.position.x}, ${target1.position.y})`);
            console.log(`  target2: ${target2.name} (${target2.position.x}, ${target2.position.y})`);
        } else {
            console.log(`  target1: ${target1 ? target1.name : 'null'}`);
            console.log(`  target2: ${target2 ? target2.name : 'null'}`);
        }
    }
}
