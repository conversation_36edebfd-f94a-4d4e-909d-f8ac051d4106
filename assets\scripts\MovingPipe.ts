import { _decorator, Component, Node, Vec3 } from 'cc';
import { Pipe } from './Pipe';
import { GameDifficulty } from './GameDifficulty';
const { ccclass, property } = _decorator;

@ccclass('MovingPipe')
export class MovingPipe extends Component {

    @property
    verticalSpeed: number = 50; // 基础垂直移动速度

    @property
    verticalRange: number = 250; // 上下移动的范围

    // 不同难度的垂直速度倍率
    private readonly VERTICAL_SPEED_MULTIPLIER_NORMAL: number = 1.0;
    private readonly VERTICAL_SPEED_MULTIPLIER_HARD: number = 1.5;

    private _originalY: number = 0; // 初始Y坐标
    private _direction: number = 1; // 移动方向：1向上，-1向下
    private _isMoving: boolean = false; // 是否上下移动
    private _pipe: Pipe = null; // Pipe组件引用

    start() {
        // 获取Pipe组件
        this._pipe = this.getComponent(Pipe);
        if (!this._pipe) {
            console.error("MovingPipe: 未找到Pipe组件!");
            return;
        }

        // 保存初始Y坐标
        this._originalY = this.node.position.y;

        // 根据当前难度决定是否启用移动
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度设置移动概率和行为
        switch (currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                // 标准难度：30%概率上下移动
                this._isMoving = Math.random() < 0.3;
                break;

            case GameDifficulty.DIFFICULTY_HARD:
                // 困难难度：50%概率上下移动
                this._isMoving = Math.random() < 0.5;
                break;

            default:
                // 轻松难度：不移动
                this._isMoving = false;
                break;
        }

        // 如果启用移动，设置初始方向
        if (this._isMoving) {
            console.log(`创建了一个会上下移动的管道，难度: ${currentDifficulty}`);
            // 随机初始方向
            this._direction = Math.random() > 0.5 ? 1 : -1;
        }
    }

    update(deltaTime: number) {
        // 如果不需要移动，则不执行任何操作
        if (!this._isMoving) return;

        // 获取当前位置
        const currentPos = this.node.position;

        // 根据难度获取垂直速度倍率
        let speedMultiplier = this.VERTICAL_SPEED_MULTIPLIER_NORMAL;
        const currentDifficulty = GameDifficulty.getDifficulty();

        if (currentDifficulty === GameDifficulty.DIFFICULTY_HARD) {
            speedMultiplier = this.VERTICAL_SPEED_MULTIPLIER_HARD;
        }

        // 计算垂直移动距离
        const verticalMove = this.verticalSpeed * speedMultiplier * deltaTime * this._direction;

        // 更新位置
        this.node.setPosition(currentPos.x, currentPos.y + verticalMove, currentPos.z);

        // 检查是否达到移动范围边界
        const distanceFromOriginal = Math.abs(this.node.position.y - this._originalY);
        if (distanceFromOriginal >= this.verticalRange / 2) {
            // 改变方向
            this._direction *= -1;
        }
    }
}
