import { _decorator, Component, input, Input, KeyCode } from 'cc';
import { BackgroundManager } from './BackgroundManager';
const { ccclass, property } = _decorator;

/**
 * 背景调试器 - 用于调试背景组件配置
 */
@ccclass('BackgroundDebugger')
export class BackgroundDebugger extends Component {

    start() {
        // 注册键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        
        // 延迟检查背景配置
        this.scheduleOnce(() => {
            this.checkBackgroundConfiguration();
        }, 1);
    }

    onDestroy() {
        // 取消键盘事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: any) {
        switch (event.keyCode) {
            case KeyCode.KEY_C:
                // C键检查配置
                this.checkBackgroundConfiguration();
                break;
            case KeyCode.KEY_D:
                // D键显示详细信息
                this.showDetailedInfo();
                break;
        }
    }

    /**
     * 检查背景配置
     */
    private checkBackgroundConfiguration(): void {
        const bgManager = BackgroundManager.getInstance();
        if (!bgManager) {
            console.error("BackgroundManager未找到！");
            return;
        }

        console.log("=== 背景配置检查 ===");
        
        // 检查每套背景的配置
        for (let i = 1; i <= 3; i++) {
            console.log(`\n--- 背景套装 ${i} ---`);
            
            let bgNode, pipeSpawnNode, landNode;
            switch (i) {
                case 1:
                    bgNode = bgManager['bgSet1'];
                    pipeSpawnNode = bgManager['pipeSpawnSet1'];
                    landNode = bgManager['landSet1'];
                    break;
                case 2:
                    bgNode = bgManager['bgSet2'];
                    pipeSpawnNode = bgManager['pipeSpawnSet2'];
                    landNode = bgManager['landSet2'];
                    break;
                case 3:
                    bgNode = bgManager['bgSet3'];
                    pipeSpawnNode = bgManager['pipeSpawnSet3'];
                    landNode = bgManager['landSet3'];
                    break;
            }

            // 检查节点是否存在
            console.log(`Bg节点: ${bgNode ? '✓' : '✗'} ${bgNode?.name || 'null'}`);
            console.log(`PipeSpawn节点: ${pipeSpawnNode ? '✓' : '✗'} ${pipeSpawnNode?.name || 'null'}`);
            console.log(`Land节点: ${landNode ? '✓' : '✗'} ${landNode?.name || 'null'}`);

            // 检查组件
            if (bgNode) {
                const bgMoveBg = bgNode.getComponent('MoveBg');
                console.log(`  Bg MoveBg组件: ${bgMoveBg ? '✓' : '✗'}`);
                
                // 检查子节点
                console.log(`  Bg子节点数量: ${bgNode.children.length}`);
                bgNode.children.forEach((child, index) => {
                    console.log(`    子节点${index}: ${child.name}`);
                });
            }

            if (pipeSpawnNode) {
                const pipeSpawner = pipeSpawnNode.getComponent('PipeSpawner');
                console.log(`  PipeSpawner组件: ${pipeSpawner ? '✓' : '✗'}`);
                if (pipeSpawner) {
                    console.log(`    pipePrefab: ${pipeSpawner['pipePrefab'] ? '✓' : '✗'}`);
                    console.log(`    spawnRate: ${pipeSpawner['spawnRate']}`);
                }
            }

            if (landNode) {
                const landMoveBg = landNode.getComponent('MoveBg');
                console.log(`  Land MoveBg组件: ${landMoveBg ? '✓' : '✗'}`);
                
                // 检查子节点
                console.log(`  Land子节点数量: ${landNode.children.length}`);
                landNode.children.forEach((child, index) => {
                    console.log(`    子节点${index}: ${child.name}`);
                });
            }
        }

        console.log(`\n当前激活的背景套装: ${bgManager.getCurrentBackgroundSet()}`);
    }

    /**
     * 显示详细信息
     */
    private showDetailedInfo(): void {
        const bgManager = BackgroundManager.getInstance();
        if (!bgManager) {
            console.error("BackgroundManager未找到！");
            return;
        }

        console.log("=== 当前激活背景详细信息 ===");
        
        const currentSet = bgManager.getCurrentBackgroundSet();
        console.log(`当前背景套装: ${currentSet}`);

        const currentBgMoving = bgManager.getCurrentBgMoving();
        const currentLandMoving = bgManager.getCurrentLandMoving();
        const currentPipeSpawner = bgManager.getCurrentPipeSpawner();

        console.log(`当前背景MoveBg: ${currentBgMoving ? '✓' : '✗'}`);
        if (currentBgMoving) {
            console.log(`  moveSpeed: ${currentBgMoving['moveSpeed']}`);
            console.log(`  _canMoving: ${currentBgMoving['_canMoving']}`);
            console.log(`  target1ToMove: ${currentBgMoving['target1ToMove']?.name || 'null'}`);
            console.log(`  target2ToMove: ${currentBgMoving['target2ToMove']?.name || 'null'}`);
        }

        console.log(`当前地面MoveBg: ${currentLandMoving ? '✓' : '✗'}`);
        if (currentLandMoving) {
            console.log(`  moveSpeed: ${currentLandMoving['moveSpeed']}`);
            console.log(`  _canMoving: ${currentLandMoving['_canMoving']}`);
            console.log(`  target1ToMove: ${currentLandMoving['target1ToMove']?.name || 'null'}`);
            console.log(`  target2ToMove: ${currentLandMoving['target2ToMove']?.name || 'null'}`);
        }

        console.log(`当前PipeSpawner: ${currentPipeSpawner ? '✓' : '✗'}`);
        if (currentPipeSpawner) {
            console.log(`  pipePrefab: ${currentPipeSpawner['pipePrefab'] ? '✓' : '✗'}`);
            console.log(`  spawnRate: ${currentPipeSpawner['spawnRate']}`);
            console.log(`  _isSpawning: ${currentPipeSpawner['_isSpawning']}`);
            console.log(`  子管道数量: ${currentPipeSpawner.node.children.length}`);
        }
    }
}
