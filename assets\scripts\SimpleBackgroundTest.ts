import { _decorator, Component, input, Input, KeyCode } from 'cc';
import { BackgroundManager } from './BackgroundManager';
const { ccclass, property } = _decorator;

/**
 * 简单的背景测试器
 */
@ccclass('SimpleBackgroundTest')
export class SimpleBackgroundTest extends Component {

    start() {
        // 注册键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log("SimpleBackgroundTest 已启动，按1、2、3键切换背景");
    }

    onDestroy() {
        // 取消键盘事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: any) {
        try {
            const bgManager = BackgroundManager.getInstance();
            if (!bgManager) {
                console.warn("BackgroundManager未找到");
                return;
            }

            switch (event.keyCode) {
                case KeyCode.DIGIT_1:
                    console.log("测试：直接切换到背景套装1");
                    bgManager.switchToBackgroundSet(1);
                    break;
                case KeyCode.DIGIT_2:
                    console.log("测试：直接切换到背景套装2");
                    bgManager.switchToBackgroundSet(2);
                    break;
                case KeyCode.DIGIT_3:
                    console.log("测试：直接切换到背景套装3");
                    bgManager.switchToBackgroundSet(3);
                    break;
                case KeyCode.KEY_T:
                    // T键测试当前配置
                    this.testCurrentConfiguration();
                    break;
            }
        } catch (error) {
            console.error("背景切换出错:", error);
        }
    }

    private testCurrentConfiguration(): void {
        const bgManager = BackgroundManager.getInstance();
        if (!bgManager) {
            console.warn("BackgroundManager未找到");
            return;
        }

        console.log("=== 当前配置测试 ===");
        console.log(`当前背景套装: ${bgManager.getCurrentBackgroundSet()}`);

        const currentBg = bgManager.getCurrentBgMoving();
        const currentLand = bgManager.getCurrentLandMoving();
        const currentPipe = bgManager.getCurrentPipeSpawner();

        console.log(`背景MoveBg: ${currentBg ? '✓' : '✗'}`);
        console.log(`地面MoveBg: ${currentLand ? '✓' : '✗'}`);
        console.log(`管道生成器: ${currentPipe ? '✓' : '✗'}`);

        if (currentBg) {
            console.log(`  背景target1: ${currentBg.target1ToMove?.name || 'null'}`);
            console.log(`  背景target2: ${currentBg.target2ToMove?.name || 'null'}`);
        }

        if (currentLand) {
            console.log(`  地面target1: ${currentLand.target1ToMove?.name || 'null'}`);
            console.log(`  地面target2: ${currentLand.target2ToMove?.name || 'null'}`);
        }

        if (currentPipe) {
            console.log(`  管道生成器节点: ${currentPipe.node?.name || 'null'}`);
            console.log(`  管道预制体: ${currentPipe.pipePrefab ? '✓' : '✗'}`);
            console.log(`  是否正在生成: ${currentPipe['_isSpawning']}`);
            console.log(`  子管道数量: ${currentPipe.node?.children?.length || 0}`);
        }

        // 检查所有背景套装的管道生成器状态
        console.log("\n=== 所有管道生成器状态 ===");
        for (let i = 1; i <= 3; i++) {
            let pipeSpawnNode;
            switch (i) {
                case 1:
                    pipeSpawnNode = bgManager['pipeSpawnSet1'];
                    break;
                case 2:
                    pipeSpawnNode = bgManager['pipeSpawnSet2'];
                    break;
                case 3:
                    pipeSpawnNode = bgManager['pipeSpawnSet3'];
                    break;
            }

            if (pipeSpawnNode) {
                const spawner = pipeSpawnNode.getComponent('PipeSpawner');
                console.log(`套装${i}: 节点=${pipeSpawnNode.name}, 激活=${pipeSpawnNode.active}, 组件=${spawner ? '✓' : '✗'}, 子节点=${pipeSpawnNode.children.length}`);
                if (spawner) {
                    console.log(`  生成状态=${spawner['_isSpawning']}, 预制体=${spawner.pipePrefab ? '✓' : '✗'}`);
                }
            } else {
                console.log(`套装${i}: 节点=null`);
            }
        }
    }
}
