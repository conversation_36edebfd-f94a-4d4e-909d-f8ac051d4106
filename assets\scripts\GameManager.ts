import { _decorator, AudioClip, Component, Label, Node } from 'cc';
import { <PERSON> } from './<PERSON>';
import { MoveBg } from './MoveBg';
import { PipeSpawner } from './PipeSpawner';
import { CoinSpawner } from './CoinSpawner';
import { GameReadyUI } from './UI/GameReadyUI';
import { GameData } from './GameData';
import { GameOverUI } from './UI/GameOverUI';
import { AudioMgr } from './AudioMgr';
import { CoinLabel } from './UI/CoinLabel';
import { EnergyManager } from './EnergyManager';
import { EnergyExhaustedPanel } from './EnergyExhaustedPanel';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { FogEffect } from './FogEffect';
import { SnowSpawner } from './SnowSpawner';
import { WindChallengeManager } from './WindChallengeManager';
import { BackgroundManager } from './BackgroundManager';
const { ccclass, property } = _decorator;


enum GameState{
    Ready,
    Gaming,
    GameOver
}

@ccclass('GameManager')
export class GameManager extends Component {

    private static _inst:GameManager = null;
    public static inst(){
        return this._inst;
    }

    @property
    moveSpeed:number = 200; // 基础移动速度（轻松难度）

    // 不同难度的速度倍率
    private readonly SPEED_MULTIPLIER_EASY: number = 1.0;
    private readonly SPEED_MULTIPLIER_NORMAL: number = 1.2;
    private readonly SPEED_MULTIPLIER_HARD: number = 1.5;

    // 不同难度的重力倍率（基于轻松难度2.5g）
    private readonly GRAVITY_VALUE_EASY: number = 2.5;
    private readonly GRAVITY_VALUE_NORMAL: number = 3.3;  // 新的标准难度重力值
    private readonly GRAVITY_VALUE_HARD: number = 4.0;    // 新的困难难度重力值

    // 获取当前难度下的移动速度
    public getCurrentMoveSpeed(): number {
        const difficulty = GameDifficulty.getDifficulty();

        switch(difficulty) {
            case GameDifficulty.DIFFICULTY_EASY:
                return this.moveSpeed * this.SPEED_MULTIPLIER_EASY;
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.moveSpeed * this.SPEED_MULTIPLIER_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.moveSpeed * this.SPEED_MULTIPLIER_HARD;
            default:
                return this.moveSpeed;
        }
    }

    // 获取当前难度下的重力值
    public getCurrentGravityValue(): number {
        const difficulty = GameDifficulty.getDifficulty();

        switch(difficulty) {
            case GameDifficulty.DIFFICULTY_EASY:
                return this.GRAVITY_VALUE_EASY;
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.GRAVITY_VALUE_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.GRAVITY_VALUE_HARD;
            default:
                return this.GRAVITY_VALUE_EASY;
        }
    }

    @property('Bird')
    bird:Bird = null;

    // 背景管理器
    @property(BackgroundManager)
    backgroundManager:BackgroundManager = null;

    // 保留原有的引用作为兼容（将通过backgroundManager动态获取）
    @property(MoveBg)
    bgMoving:MoveBg = null;
    @property(MoveBg)
    landMoving:MoveBg = null;
    @property(PipeSpawner)
    pipeSpawner:PipeSpawner = null;
    @property(CoinSpawner)
    coinSpawner:CoinSpawner = null;
    @property(GameReadyUI)
    gameReadyUI:GameReadyUI = null;
    @property(Node)
    gamingUI:Node = null;
    @property(GameOverUI)
    gameOverUI:GameOverUI = null;

    @property(EnergyExhaustedPanel)
    energyExhaustedPanel:EnergyExhaustedPanel = null;

    // 挑战模式相关组件
    @property(Node)
    fogEffect:Node = null; // 大雾效果节点

    @property(Node)
    snowSpawner:Node = null; // 雪花生成器节点

    @property(WindChallengeManager)
    windChallengeManager:WindChallengeManager = null; // 大风吹挑战管理器

    @property(Label)
    scoreLabel:Label = null;
    @property(CoinLabel)
    coinLabel:CoinLabel = null;
    @property(AudioClip)
    bgAudio:AudioClip = null;
    @property(AudioClip)
    gameOverAudio:AudioClip = null;

    curGS:GameState = GameState.Ready;

    onLoad() {
        GameManager._inst=this;
    }

    protected start(): void {
        this.transitionToReadyState();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            console.log("GameManager: 播放游戏场景BGM");
            AudioMgr.inst.play(this.bgAudio, 0.1);
        } else {
            console.error("GameManager: bgAudio未设置！请在编辑器中将bgm_new.mp3拖拽到GameManager组件的bgAudio属性中");
        }

        // 确保EnergyManager在游戏场景中也能更新
        this.scheduleEnergyUpdate();

        // 处理挑战模式
        this.handleChallengeMode();
    }

    /**
     * 处理挑战模式
     */
    private handleChallengeMode(): void {
        // 获取当前挑战模式
        const currentMode = ChallengeMode.getMode();
        console.log(`当前挑战模式: ${currentMode}`);

        // 处理大风吹模式
        if (currentMode === ChallengeModeType.WIND) {
            console.log("大风吹模式已激活");
            this.activateWindMode();
        }

        // 处理大雾模式
        if (currentMode === ChallengeModeType.FOG) {
            console.log("大雾模式已激活");
            this.activateFogMode();
        }

        // 处理大雪模式
        if (currentMode === ChallengeModeType.SNOW) {
            console.log("大雪模式已激活");
            this.activateSnowMode();
        }
    }

    /**
     * 激活大风吹模式
     */
    private activateWindMode(): void {
        // 检查当前模式是否为大风吹模式
        if (ChallengeMode.getMode() !== ChallengeModeType.WIND) {
            console.log("非大风吹模式，不激活大风吹功能");
            return;
        }

        // 检查是否有大风吹挑战管理器
        if (this.windChallengeManager) {
            console.log("大风吹模式：激活大风吹挑战管理器");
            // 大风吹管理器会在自己的start方法中初始化
        } else {
            console.error("未找到大风吹挑战管理器，请在编辑器中设置");
        }
    }

    /**
     * 激活大雾模式
     */
    private activateFogMode(): void {
        // 再次检查当前模式是否为大雾模式，防止其他模式也显示雾效果
        if (ChallengeMode.getMode() !== ChallengeModeType.FOG) {
            console.log("非大雾模式，不显示雾效果");
            // 如果不是大雾模式，确保雾效果节点处于隐藏状态
            if (this.fogEffect) {
                this.fogEffect.active = false;
            }
            return;
        }

        // 检查是否有雾效果节点
        if (this.fogEffect) {
            console.log("大雾模式：显示大雾效果");
            // 激活雾效果节点
            this.fogEffect.active = true;

            // 如果节点上有FogEffect组件，调用其显示方法
            const fogEffectComp = this.fogEffect.getComponent(FogEffect);
            if (fogEffectComp) {
                fogEffectComp.showFogEffect(); // 直接调用显示方法，而不是checkFogMode
            }
        } else {
            console.error("未找到雾效果节点，请在编辑器中设置");
        }
    }

    /**
     * 激活大雪模式
     */
    private activateSnowMode(): void {
        // 再次检查当前模式是否为大雪模式
        if (ChallengeMode.getMode() !== ChallengeModeType.SNOW) {
            console.log("非大雪模式，不显示雪效果");
            // 如果不是大雪模式，确保雪花生成器处于隐藏状态
            if (this.snowSpawner) {
                this.snowSpawner.active = false;
            }
            return;
        }

        // 检查是否有雪花生成器节点
        if (this.snowSpawner) {
            console.log("大雪模式：激活雪花生成器");
            // 激活雪花生成器节点
            this.snowSpawner.active = true;
        } else {
            console.error("未找到雪花生成器节点，请在编辑器中设置");
        }
    }

    transitionToReadyState(){
        this.curGS = GameState.Ready;
        // 重置本局收集的金币数
        GameData.resetSessionCoins();

        // 重置体力消耗标志，允许下一局游戏消耗体力
        this._energyConsumed = false;

        this.bird.disableControl();

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.disableMoving();
        if (currentLandMoving) currentLandMoving.disableMoving();
        if (currentPipeSpawner) currentPipeSpawner.pause();
        if (this.coinSpawner) {
            this.coinSpawner.pause();
        }

        // 暂停雪花生成器
        if (this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                snowSpawnerComp.pause();
            }
        }

        // 重置大风吹模式状态
        if (this.windChallengeManager && ChallengeMode.getMode() === ChallengeModeType.WIND) {
            this.windChallengeManager.reset();
        }

        this.gamingUI.active=false;
        this.gameOverUI.hide();
        this.gameReadyUI.node.active=true;
    }
    // 防止重复消耗体力的标志
    private _energyConsumed: boolean = false;

    transitionToGamingState(){
        // 防止重复进入Gaming状态
        if (this.curGS == GameState.Gaming) {
            console.log("已经在Gaming状态，忽略重复切换");
            return;
        }

        console.log("切换到Gaming状态");

        // 只在本次游戏中第一次调用时消耗体力
        if (!this._energyConsumed) {
            // 尝试消耗体力值
            const energyManager = EnergyManager.getInstance();

            if (energyManager) {
                // 尝试消耗体力值
                const success = energyManager.consumeEnergy();
                if (!success) {
                    console.log("体力不足，无法开始游戏");
                    // 显示体力不足提示面板
                    if (this.energyExhaustedPanel) {
                        this.energyExhaustedPanel.show();
                    }
                    return;
                }
                console.log("成功消耗体力，开始游戏");
                // 标记已消耗体力，防止重复消耗
                this._energyConsumed = true;
            } else {
                console.warn("未找到EnergyManager实例，跳过体力检查");
            }
        } else {
            console.log("本局游戏已消耗过体力，不再重复消耗");
        }

        this.curGS = GameState.Gaming;

        this.bird.enableControl();

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.enableMoving();
        if (currentLandMoving) currentLandMoving.enableMoving();

        // 根据是否为大风吹模式决定启动方式
        if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
            // 大风吹模式：使用距离控制，不启动定时器
            console.log("GameManager: 大风吹模式 - 使用距离控制生成");
            if (currentPipeSpawner) currentPipeSpawner.startWindMode();
            if (this.coinSpawner) {
                (this.coinSpawner as any).startWindMode();
            }
        } else {
            // 普通模式：使用时间控制
            if (currentPipeSpawner) currentPipeSpawner.start();
            if (this.coinSpawner) {
                console.log("GameManager: 启动金币生成器");
                this.coinSpawner.startSpawning();
            } else {
                console.error("GameManager错误: coinSpawner未设置!");
            }
        }

        // 启动雪花生成器（如果是大雪模式）
        if (ChallengeMode.getMode() === ChallengeModeType.SNOW && this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                console.log("GameManager: 启动雪花生成器");
                snowSpawnerComp.startSpawning();
            }
        }

        this.gameReadyUI.node.active=false;
        this.gamingUI.active=true;
    }
    transitionToGameOverState(){
        if(this.curGS==GameState.GameOver)return;
        this.curGS = GameState.GameOver;

        this.bird.disableControlNotRGD();

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.disableMoving();
        if (currentLandMoving) currentLandMoving.disableMoving();
        if (currentPipeSpawner) currentPipeSpawner.pause();
        if (this.coinSpawner) {
            this.coinSpawner.pause();
        }

        // 暂停雪花生成器
        if (this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                snowSpawnerComp.pause();
            }
        }

        this.gamingUI.active=false;

        this.gameOverUI.show( GameData.getScore() ,GameData.getBestScore());
        // 注意：saveScore 已移至 GameOverUI.show 方法中，避免重复调用
        AudioMgr.inst.stop();
        AudioMgr.inst.playOneShot(this.gameOverAudio);

        // 输出本局收集的金币数
        console.log(`游戏结束! 本局收集金币: ${GameData.getSessionCoins()}, 总金币: ${GameData.getTotalCoins()}`);
    }

    addScore(count:number=1){
        GameData.addScore(count);
        this.scoreLabel.string = GameData.getScore().toString();
    }

    /**
     * 更新金币显示
     * @param count 增加的金币数量，默认为1
     */
    addCoin(count:number=1){
        GameData.addCoin(count);
        // 更新金币显示
        if (this.coinLabel) {
            this.coinLabel.updateDisplay();
        }
    }

    /**
     * 为体力系统添加额外的更新检查
     */
    private scheduleEnergyUpdate() {
        // 每秒检查体力恢复
        this.schedule(this.checkEnergyRecovery, 1.0);
    }

    /**
     * 检查体力恢复，确保在游戏场景中体力恢复计时器能正常工作
     */
    private checkEnergyRecovery() {
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) return;

        // 主动触发体力检查
        if (energyManager.getTimeUntilNextRecover() <= 0 &&
            energyManager.getCurrentEnergy() < energyManager.getMaxEnergy()) {
            console.log("GameManager: 强制检查体力恢复");
            energyManager.forceCheckEnergyRecover();
        }
    }

    update(deltaTime: number) {
        // 在大风吹模式下更新距离
        if (this.windChallengeManager && ChallengeMode.getMode() === ChallengeModeType.WIND && this.curGS === GameState.Gaming) {
            this.windChallengeManager.updateDistance(deltaTime);
        }
    }

    onDestroy() {
        // 取消体力更新检查
        this.unschedule(this.checkEnergyRecovery);
    }

    /**
     * 获取当前激活的背景移动组件
     */
    private getCurrentBgMoving(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentBgMoving();
        }
        return this.bgMoving; // 兼容模式
    }

    /**
     * 获取当前激活的地面移动组件
     */
    private getCurrentLandMoving(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentLandMoving();
        }
        return this.landMoving; // 兼容模式
    }

    /**
     * 获取当前激活的管道生成器组件
     */
    private getCurrentPipeSpawner(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentPipeSpawner();
        }
        return this.pipeSpawner; // 兼容模式
    }

    /**
     * 切换背景套装
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    public switchBackgroundSet(setNumber: number): void {
        if (this.backgroundManager) {
            console.log(`GameManager: 切换到背景套装 ${setNumber}`);
            this.backgroundManager.switchToBackgroundSet(setNumber);
        } else {
            console.warn("BackgroundManager未设置，无法切换背景");
        }
    }

    /**
     * 获取当前游戏状态（供BackgroundManager使用）
     */
    public getCurrentGameState(): number {
        return this.curGS;
    }

}


