import { _decorator, Component, director, Label, Node } from 'cc';
import { GameData } from '../GameData';
import { EnergyManager } from '../EnergyManager';
import { GameManager } from '../GameManager';
import { AudioMgr } from '../AudioMgr';
const { ccclass, property } = _decorator;

@ccclass('GameOverUI')
export class GameOverUI extends Component {

    @property(Label)
    curScoreLabel:Label = null;
    @property(Label)
    bestScoreLabel:Label = null;
    @property(Label)
    sessionCoinsLabel:Label = null;

    @property(Node)
    newSprite:Node = null;

    @property([Node])
    medalArray:Node[] = [];

    public show(curScore:number,bestScrore:number){
        this.node.active=true;
        this.curScoreLabel.string = curScore.toString();
        this.bestScoreLabel.string = bestScrore.toString();

        // 显示本局收集的金币数
        if (this.sessionCoinsLabel) {
            const sessionCoins = GameData.getSessionCoins();
            this.sessionCoinsLabel.string = sessionCoins.toString();
            console.log("显示本局金币数：", sessionCoins);
        }

        // 显示"NEW"标志
        if(curScore>bestScrore){
            this.newSprite.active = true;
        }else{
            this.newSprite.active = false;
        }

        // 隐藏所有奖牌
        for (let i = 0; i < this.medalArray.length; i++) {
            this.medalArray[i].active = false;
        }

        // 先保存分数，确保历史记录已更新
        GameData.saveScore();

        // 获取当前分数在历史前三高分中的排名
        const rank = GameData.getCurrentRank();

        // 获取并输出最高三次分数（调试用）
        const topScores = GameData.getTopScores();
        console.log("当前最高三次分数：", topScores);
        console.log("当前分数：", curScore);
        console.log("当前排名：", rank);
        console.log("奖牌索引：", (rank >= 0 && rank < 3) ? (3 - rank) : 0);

        // 根据排名显示对应的奖牌
        // rank: 0-金牌, 1-银牌, 2-铜牌, -1-木牌(未进入前三)
        if (rank >= 0 && rank < 3) {
            // 显示对应的奖牌（金、银、铜）
            this.medalArray[3 - rank].active = true; // 3-金, 2-银, 1-铜
            console.log("显示奖牌：", 3 - rank); // 调试信息
        } else {
            // 未进入前三，显示木牌
            this.medalArray[0].active = true;
            console.log("显示木牌"); // 调试信息
        }
    }

    public hide(){
        this.node.active=false;
    }

    onPlayButtonClick(){
        // 检查体力是否足够
        const energyManager = EnergyManager.getInstance();
        if (energyManager && energyManager.getCurrentEnergy() < EnergyManager.ENERGY_PER_GAME) {
            console.log("体力不足，无法重新开始游戏");
            // 获取GameManager实例
            const gameManager = GameManager.inst();
            if (gameManager && gameManager.energyExhaustedPanel) {
                // 显示体力不足提示面板
                gameManager.energyExhaustedPanel.show();
            }
            return;
        }

        // 重置分数，解决分数累计的问题
        GameData.resetScore();
        director.loadScene(director.getScene().name);
    }

    // 点击返回主页按钮
    onHomeButtonClick(){
        // 重置分数
        GameData.resetScore();

        // 不再停止音乐，让主菜单场景自己处理BGM的播放
        // 注意：我们不再调用AudioMgr.inst.stop()

        // 加载Home场景
        director.loadScene('Home');
    }
}


