import { _decorator, Component, Node } from 'cc';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
const { ccclass, property } = _decorator;

/**
 * 背景管理器 - 管理三套可切换的背景
 */
@ccclass('BackgroundManager')
export class BackgroundManager extends Component {

    // 第一套背景（当前默认）
    @property(Node)
    bgSet1: Node = null;
    @property(Node)
    pipeSpawnSet1: Node = null;
    @property(Node)
    landSet1: Node = null;

    // 第二套背景
    @property(Node)
    bgSet2: Node = null;
    @property(Node)
    pipeSpawnSet2: Node = null;
    @property(Node)
    landSet2: Node = null;

    // 第三套背景
    @property(Node)
    bgSet3: Node = null;
    @property(Node)
    pipeSpawnSet3: Node = null;
    @property(Node)
    landSet3: Node = null;

    // 当前激活的背景套装 (1, 2, 3)
    private currentBackgroundSet: number = 1;

    // 静态实例
    private static _instance: BackgroundManager = null;

    public static getInstance(): BackgroundManager {
        return this._instance;
    }

    onLoad() {
        BackgroundManager._instance = this;
    }

    start() {
        // 简单的初始化，只设置当前背景套装为1
        this.currentBackgroundSet = 1;
        console.log("BackgroundManager初始化完成");
    }

    /**
     * 切换到指定的背景套装
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    public switchToBackgroundSet(setNumber: number): void {
        if (setNumber < 1 || setNumber > 3) {
            console.error(`无效的背景套装编号: ${setNumber}`);
            return;
        }

        console.log(`切换到背景套装 ${setNumber}`);

        // 先暂停所有管道生成器和移动组件
        this.pauseAllBackgroundSets();

        // 清理所有现有的管道
        this.clearAllPipes();

        this.currentBackgroundSet = setNumber;

        // 隐藏所有背景套装
        this.setBackgroundSetActive(1, false);
        this.setBackgroundSetActive(2, false);
        this.setBackgroundSetActive(3, false);

        // 激活指定的背景套装
        this.setBackgroundSetActive(setNumber, true);

        // 设置MoveBg组件的参数
        this.configureMoveBgParameters(setNumber);

        console.log(`背景切换完成，当前套装: ${setNumber}`);
    }

    /**
     * 设置指定背景套装的激活状态
     * @param setNumber 背景套装编号
     * @param active 是否激活
     */
    private setBackgroundSetActive(setNumber: number, active: boolean): void {
        let bgNode: Node = null;
        let pipeSpawnNode: Node = null;
        let landNode: Node = null;

        switch (setNumber) {
            case 1:
                bgNode = this.bgSet1;
                pipeSpawnNode = this.pipeSpawnSet1;
                landNode = this.landSet1;
                break;
            case 2:
                bgNode = this.bgSet2;
                pipeSpawnNode = this.pipeSpawnSet2;
                landNode = this.landSet2;
                break;
            case 3:
                bgNode = this.bgSet3;
                pipeSpawnNode = this.pipeSpawnSet3;
                landNode = this.landSet3;
                break;
        }

        if (bgNode) bgNode.active = active;
        if (pipeSpawnNode) pipeSpawnNode.active = active;
        if (landNode) landNode.active = active;

        console.log(`背景套装 ${setNumber} ${active ? '激活' : '隐藏'}`);
    }

    /**
     * 获取当前激活的背景套装编号
     */
    public getCurrentBackgroundSet(): number {
        return this.currentBackgroundSet;
    }

    /**
     * 获取当前激活的背景移动组件
     */
    public getCurrentBgMoving(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.bgSet1?.getComponent('MoveBg');
            case 2:
                return this.bgSet2?.getComponent('MoveBg');
            case 3:
                return this.bgSet3?.getComponent('MoveBg');
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的地面移动组件
     */
    public getCurrentLandMoving(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.landSet1?.getComponent('MoveBg');
            case 2:
                return this.landSet2?.getComponent('MoveBg');
            case 3:
                return this.landSet3?.getComponent('MoveBg');
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的管道生成器组件
     */
    public getCurrentPipeSpawner(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.pipeSpawnSet1?.getComponent('PipeSpawner');
            case 2:
                return this.pipeSpawnSet2?.getComponent('PipeSpawner');
            case 3:
                return this.pipeSpawnSet3?.getComponent('PipeSpawner');
            default:
                return null;
        }
    }

    /**
     * 循环切换到下一套背景
     */
    public switchToNextBackground(): void {
        const nextSet = (this.currentBackgroundSet % 3) + 1;
        this.switchToBackgroundSet(nextSet);
    }

    /**
     * 循环切换到上一套背景
     */
    public switchToPreviousBackground(): void {
        const prevSet = this.currentBackgroundSet === 1 ? 3 : this.currentBackgroundSet - 1;
        this.switchToBackgroundSet(prevSet);
    }

    /**
     * 暂停所有背景套装的组件
     */
    private pauseAllBackgroundSets(): void {
        // 暂停所有管道生成器
        const pipeSpawners = [
            this.pipeSpawnSet1?.getComponent('PipeSpawner'),
            this.pipeSpawnSet2?.getComponent('PipeSpawner'),
            this.pipeSpawnSet3?.getComponent('PipeSpawner')
        ];

        pipeSpawners.forEach(spawner => {
            if (spawner) {
                spawner.pause();
            }
        });

        // 暂停所有移动组件
        const bgMovers = [
            this.bgSet1?.getComponent('MoveBg'),
            this.bgSet2?.getComponent('MoveBg'),
            this.bgSet3?.getComponent('MoveBg')
        ];

        const landMovers = [
            this.landSet1?.getComponent('MoveBg'),
            this.landSet2?.getComponent('MoveBg'),
            this.landSet3?.getComponent('MoveBg')
        ];

        bgMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        landMovers.forEach(mover => {
            if (mover) {
                mover.disableMoving();
            }
        });

        console.log("已暂停所有背景套装的组件");
    }

    /**
     * 清理所有管道
     */
    private clearAllPipes(): void {
        const pipeSpawners = [
            this.pipeSpawnSet1,
            this.pipeSpawnSet2,
            this.pipeSpawnSet3
        ];

        pipeSpawners.forEach(spawner => {
            if (spawner) {
                // 销毁所有子节点（管道）
                const children = spawner.children.slice(); // 创建副本避免遍历时修改数组
                children.forEach(child => {
                    if (child) {
                        child.destroy();
                    }
                });
            }
        });

        console.log("已清理所有管道");
    }

    /**
     * 配置MoveBg组件的参数
     * @param setNumber 背景套装编号
     */
    private configureMoveBgParameters(setNumber: number): void {
        let bgNode: Node = null;
        let landNode: Node = null;
        let triggerX: number = -730;
        let offsetX: number = 728;

        switch (setNumber) {
            case 1:
                bgNode = this.bgSet1;
                landNode = this.landSet1;
                triggerX = -730;
                offsetX = 728;
                break;
            case 2:
                bgNode = this.bgSet2;
                landNode = this.landSet2;
                triggerX = -730;
                offsetX = 728;
                break;
            case 3:
                bgNode = this.bgSet3;
                landNode = this.landSet3;
                triggerX = -1120;
                offsetX = 1500;
                break;
        }

        // 配置背景MoveBg组件
        if (bgNode) {
            const bgMoveBg = bgNode.getComponent('MoveBg');
            if (bgMoveBg) {
                bgMoveBg.setLoopParameters(triggerX, offsetX);
                console.log(`背景套装${setNumber}的背景MoveBg参数已设置: trigger=${triggerX}, offset=${offsetX}`);
            }
        }

        // 配置地面MoveBg组件
        if (landNode) {
            const landMoveBg = landNode.getComponent('MoveBg');
            if (landMoveBg) {
                landMoveBg.setLoopParameters(triggerX, offsetX);
                console.log(`背景套装${setNumber}的地面MoveBg参数已设置: trigger=${triggerX}, offset=${offsetX}`);
            }
        }
    }

}
