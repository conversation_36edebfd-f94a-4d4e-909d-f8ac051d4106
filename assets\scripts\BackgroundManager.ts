import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 背景管理器 - 管理三套可切换的背景
 */
@ccclass('BackgroundManager')
export class BackgroundManager extends Component {

    // 第一套背景（当前默认）
    @property(Node)
    bgSet1: Node = null;
    @property(Node)
    pipeSpawnSet1: Node = null;
    @property(Node)
    landSet1: Node = null;

    // 第二套背景
    @property(Node)
    bgSet2: Node = null;
    @property(Node)
    pipeSpawnSet2: Node = null;
    @property(Node)
    landSet2: Node = null;

    // 第三套背景
    @property(Node)
    bgSet3: Node = null;
    @property(Node)
    pipeSpawnSet3: Node = null;
    @property(Node)
    landSet3: Node = null;

    // 当前激活的背景套装 (1, 2, 3)
    private currentBackgroundSet: number = 1;

    // 静态实例
    private static _instance: BackgroundManager = null;

    public static getInstance(): BackgroundManager {
        return this._instance;
    }

    onLoad() {
        BackgroundManager._instance = this;
    }

    start() {
        // 初始化时激活第一套背景，隐藏其他套
        this.switchToBackgroundSet(1);
    }

    /**
     * 切换到指定的背景套装
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    public switchToBackgroundSet(setNumber: number): void {
        if (setNumber < 1 || setNumber > 3) {
            console.error(`无效的背景套装编号: ${setNumber}`);
            return;
        }

        console.log(`切换到背景套装 ${setNumber}`);
        this.currentBackgroundSet = setNumber;

        // 隐藏所有背景套装
        this.setBackgroundSetActive(1, false);
        this.setBackgroundSetActive(2, false);
        this.setBackgroundSetActive(3, false);

        // 激活指定的背景套装
        this.setBackgroundSetActive(setNumber, true);
    }

    /**
     * 设置指定背景套装的激活状态
     * @param setNumber 背景套装编号
     * @param active 是否激活
     */
    private setBackgroundSetActive(setNumber: number, active: boolean): void {
        let bgNode: Node = null;
        let pipeSpawnNode: Node = null;
        let landNode: Node = null;

        switch (setNumber) {
            case 1:
                bgNode = this.bgSet1;
                pipeSpawnNode = this.pipeSpawnSet1;
                landNode = this.landSet1;
                break;
            case 2:
                bgNode = this.bgSet2;
                pipeSpawnNode = this.pipeSpawnSet2;
                landNode = this.landSet2;
                break;
            case 3:
                bgNode = this.bgSet3;
                pipeSpawnNode = this.pipeSpawnSet3;
                landNode = this.landSet3;
                break;
        }

        if (bgNode) bgNode.active = active;
        if (pipeSpawnNode) pipeSpawnNode.active = active;
        if (landNode) landNode.active = active;

        console.log(`背景套装 ${setNumber} ${active ? '激活' : '隐藏'}`);
    }

    /**
     * 获取当前激活的背景套装编号
     */
    public getCurrentBackgroundSet(): number {
        return this.currentBackgroundSet;
    }

    /**
     * 获取当前激活的背景移动组件
     */
    public getCurrentBgMoving(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.bgSet1?.getComponent('MoveBg');
            case 2:
                return this.bgSet2?.getComponent('MoveBg');
            case 3:
                return this.bgSet3?.getComponent('MoveBg');
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的地面移动组件
     */
    public getCurrentLandMoving(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.landSet1?.getComponent('MoveBg');
            case 2:
                return this.landSet2?.getComponent('MoveBg');
            case 3:
                return this.landSet3?.getComponent('MoveBg');
            default:
                return null;
        }
    }

    /**
     * 获取当前激活的管道生成器组件
     */
    public getCurrentPipeSpawner(): any {
        switch (this.currentBackgroundSet) {
            case 1:
                return this.pipeSpawnSet1?.getComponent('PipeSpawner');
            case 2:
                return this.pipeSpawnSet2?.getComponent('PipeSpawner');
            case 3:
                return this.pipeSpawnSet3?.getComponent('PipeSpawner');
            default:
                return null;
        }
    }

    /**
     * 循环切换到下一套背景
     */
    public switchToNextBackground(): void {
        const nextSet = (this.currentBackgroundSet % 3) + 1;
        this.switchToBackgroundSet(nextSet);
    }

    /**
     * 循环切换到上一套背景
     */
    public switchToPreviousBackground(): void {
        const prevSet = this.currentBackgroundSet === 1 ? 3 : this.currentBackgroundSet - 1;
        this.switchToBackgroundSet(prevSet);
    }
}
